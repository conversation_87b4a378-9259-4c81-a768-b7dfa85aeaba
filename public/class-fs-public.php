<?php

/**
 * Public functionality for FlashSale Core
 *
 * CACHE OPTIMIZATION:
 * - Campaigns cache: <PERSON><PERSON><PERSON> s<PERSON>ch campaigns đang hoạt động để tránh query DB nhiều lần
 * - Product promotions cache: <PERSON><PERSON><PERSON> k<PERSON>t quả khuyến mãi theo product_id để tránh xử lý lại
 * - Auto invalidation: Cache tự động clear khi campaigns/products thay đổi
 * - Performance: <PERSON><PERSON><PERSON><PERSON> từ N lần query xuống 1 lần cho mỗi sản phẩm trong cùng request
 *
 * DUPLICATE PREVENTION:
 * - Chỉ lấy 1 record tốt nhất cho mỗi combination campaign_id + product_id
 * - Tr<PERSON>h hiển thị duplicate progress bars khi có nhiều records trong DB
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FS_Public
{

    /**
     * Single instance
     */
    private static $instance = null;

    /**
     * Track if hooks are already initialized
     */
    private static $hooks_initialized = false;

    /**
     * Track rendered products to prevent duplicate display
     */
    private static $rendered_products = [];

    /**
     * Cached active campaigns
     */
    private static $active_campaigns = null;

    /**
     * Cached product promotions by product_id
     */
    private static $product_promotions_cache = [];

    /**
     * Get instance (singleton)
     */
    public static function get_instance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    public function __construct()
    {
        // Prevent duplicate initialization
        if (self::$hooks_initialized) {
            return;
        }

        $this->init_hooks();
        $this->init_cache_hooks();

        self::$hooks_initialized = true;
    }

    /**
     * Initialize hooks
     */
    private function init_hooks()
    {
        add_action('wp_enqueue_scripts', [$this, 'enqueue_scripts']);
        add_action('woocommerce_single_product_summary', [$this, 'display_promotion_info'], 25);
        add_action('woocommerce_after_shop_loop_item_title', [$this, 'display_promotion_badge'], 15);

        // Price display hooks - priority 15 để chạy sau các addon khác
        add_filter('woocommerce_get_price_html', [$this, 'modify_price_display'], 15, 2);

        // Sale price hooks - priority 5 để chạy trước các addon khác nhưng sau WooCommerce core
        add_filter('woocommerce_product_get_sale_price', [$this, 'get_sale_price'], 5, 2);
        add_filter('woocommerce_product_variation_get_sale_price', [$this, 'get_sale_price'], 5, 2);

        // Cart price hooks - priority 5 để tránh conflict với addon khuyến mãi khác
        add_filter('woocommerce_product_get_price', [$this, 'get_flashsale_price'], 5, 2);
        add_filter('woocommerce_product_variation_get_price', [$this, 'get_flashsale_price'], 5, 2);
        add_action('woocommerce_before_calculate_totals', [$this, 'apply_flashsale_cart_price'], 5, 1);

        // Order hooks - Update sold quantity when order is completed
        add_action('woocommerce_order_status_completed', [$this, 'update_sold_quantity_on_order_complete'], 10, 1);
        add_action('woocommerce_payment_complete', [$this, 'update_sold_quantity_on_payment_complete'], 10, 1);

        add_shortcode('flashsale_campaign', [$this, 'campaign_shortcode']);
    }

    /**
     * Initialize cache hooks
     */
    private function init_cache_hooks()
    {
        // Clear cache when campaigns are modified
        add_action('fs_campaign_created', [$this, 'clear_all_caches_on_campaign_change']);
        add_action('fs_campaign_updated', [$this, 'clear_all_caches_on_campaign_change']);
        add_action('fs_campaign_deleted', [$this, 'clear_all_caches_on_campaign_change']);

        // Clear cache when products are modified (in case they affect promotions)
        add_action('woocommerce_update_product', [$this, 'clear_product_cache_on_product_change']);
        add_action('woocommerce_delete_product', [$this, 'clear_product_cache_on_product_change']);
    }

    /**
     * Clear all caches when campaign changes
     */
    public function clear_all_caches_on_campaign_change($campaign_id = null)
    {
        self::clear_all_caches();
    }

    /**
     * Clear product cache when product changes
     */
    public function clear_product_cache_on_product_change($product_id)
    {
        self::clear_product_promotions_cache($product_id);
    }

    /**
     * Enqueue public scripts
     */
    public function enqueue_scripts()
    {
        wp_enqueue_style(
            'fs-public-style',
            FLASHSALE_CORE_PLUGIN_URL . 'public/css/public.css',
            [],
            FLASHSALE_CORE_VERSION
        );

        wp_enqueue_script(
            'fs-public-script',
            FLASHSALE_CORE_PLUGIN_URL . 'public/js/public.js',
            ['jquery'],
            FLASHSALE_CORE_VERSION,
            true
        );

        wp_localize_script('fs-public-script', 'fs_public', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('fs_public_nonce'),
            'strings' => [
                'flash_sale' => __('Flash Sale', 'flashsale-core'),
                'limited_time' => __('Limited Time Offer', 'flashsale-core'),
                'ends_in' => __('Ends in', 'flashsale-core'),
                'sold_out' => __('Sold Out', 'flashsale-core')
            ]
        ]);
    }

    /**
     * Display promotion info on single product page
     */
    public function display_promotion_info()
    {
        global $product;

        if (!$product) {
            return;
        }

        $product_id = $product->get_id();

        // For variable products, check all variations
        $product_ids_to_check = [$product_id];

        if ($product->is_type('variable')) {
            $variations = $product->get_available_variations();
            foreach ($variations as $variation) {
                $product_ids_to_check[] = $variation['variation_id'];
            }
        }

        // Prevent duplicate rendering for the same product
        if (isset(self::$rendered_products[$product_id])) {
            return;
        }

        // Mark this product as rendered
        self::$rendered_products[$product_id] = true;

        // Get promotions for all product IDs (main product + variations)
        $all_promotions = [];
        foreach ($product_ids_to_check as $pid) {
            $promotions = $this->get_product_promotions($pid);
            if (!empty($promotions)) {
                $all_promotions = array_merge($all_promotions, $promotions);
            }
        }

        if (empty($all_promotions)) {
            return;
        }

        // Filter out quantity promotions - they should be handled by their own addon
        $flash_sale_promotions = array_filter($all_promotions, function ($promotion) {
            return $promotion['campaign']->type !== 'quantity-promotion';
        });

        if (empty($flash_sale_promotions)) {
            return;
        }

        echo '<div class="fs-promotion-info">';

        // For variable products, show promotion info with variation support
        if ($product->is_type('variable')) {
            echo '<div class="fs-variation-promotions">';
            echo '<p class="fs-variation-notice">' . __('Promotions available for selected variations', 'flashsale-core') . '</p>';

            // Group promotions by variation
            $variation_promotions = [];
            foreach ($flash_sale_promotions as $promotion) {
                $promotion_product_id = $promotion['product_data']->product_id;
                if (!isset($variation_promotions[$promotion_product_id])) {
                    $variation_promotions[$promotion_product_id] = [];
                }
                $variation_promotions[$promotion_product_id][] = $promotion;
            }

            // Render promotions for each variation
            foreach ($variation_promotions as $vid => $promotions) {
                if ($vid == $product_id) {
                    // Main product promotions
                    $best_promotion = $this->get_best_promotion($promotions, $product);
                    if ($best_promotion) {
                        echo '<div class="fs-main-product-promotion">';
                        $this->render_promotion_info($best_promotion, $product);
                        echo '</div>';
                    }
                } else {
                    // Variation promotions
                    $variation_product = wc_get_product($vid);
                    if ($variation_product) {
                        $best_promotion = $this->get_best_promotion($promotions, $variation_product);
                        if ($best_promotion) {
                            echo '<div class="fs-variation-promotion" data-variation-id="' . esc_attr($vid) . '" style="display: none;">';
                            echo '<div class="fs-variation-info">';
                            echo '<strong>' . $variation_product->get_formatted_name() . '</strong>';
                            echo '</div>';
                            $this->render_promotion_info($best_promotion, $variation_product);
                            echo '</div>';
                        }
                    }
                }
            }
            echo '</div>';
        } else {
            // Simple product or non-variable
            $best_promotion = $this->get_best_promotion($flash_sale_promotions, $product);
            if ($best_promotion) {
                $this->render_promotion_info($best_promotion, $product);
            }
        }

        echo '</div>';
    }

    /**
     * Display promotion badge on shop loop
     */
    public function display_promotion_badge()
    {
        global $product;

        if (!$product) {
            return;
        }

        $promotions = $this->get_product_promotions($product->get_id());

        if (empty($promotions)) {
            return;
        }

        // Only show badge for flash sale campaigns, not quantity promotions
        $flash_sale_promotions = array_filter($promotions, function ($promotion) {
            return $promotion['campaign']->type !== 'quantity-promotion';
        });

        if (empty($flash_sale_promotions)) {
            return;
        }

        echo '<div class="fs-promotion-badge">';
        echo '<span class="fs-badge fs-badge-sale">' . __('Flash Sale', 'flashsale-core') . '</span>';
        echo '</div>';
    }

    /**
     * Modify price display
     */
    public function modify_price_display($price_html, $product)
    {
        if (!$product || !is_object($product)) {
            return $price_html;
        }

        $product_id = $product->get_id();
        $promotions = $this->get_product_promotions($product_id);

        if (empty($promotions)) {
            return $price_html;
        }

        // Only modify price for flash sale campaigns, not quantity promotions
        $flash_sale_promotions = array_filter($promotions, function ($promotion) {
            return $promotion['campaign']->type !== 'quantity-promotion';
        });

        if (empty($flash_sale_promotions)) {
            return $price_html;
        }

        // Get the best flash sale promotion for this product
        $best_promotion = $this->get_best_promotion($flash_sale_promotions, $product);

        if (!$best_promotion) {
            return $price_html;
        }

        // Calculate discounted price
        $original_price = $product->get_regular_price();
        $sale_price = $this->calculate_discounted_price_from_product_data($original_price, $best_promotion['product_data']);

        if ($sale_price >= $original_price) {
            return $price_html;
        }

        // Format the new price HTML
        $currency_symbol = get_woocommerce_currency_symbol();
        $original_price_formatted = wc_price($original_price);
        $sale_price_formatted = wc_price($sale_price);

        // Calculate discount percentage for display
        $discount_percentage = round((($original_price - $sale_price) / $original_price) * 100);

        $new_price_html = '<div class="fs-price-container">';
        $new_price_html .= '<span class="fs-sale-price">' . $sale_price_formatted . '</span>';
        $new_price_html .= '<span class="fs-original-price">' . $original_price_formatted . '</span>';
        $new_price_html .= '<span class="fs-discount-badge">-' . $discount_percentage . '%</span>';
        $new_price_html .= '</div>';

        return $new_price_html;
    }

    /**
     * Update sold quantity when order is completed
     */
    public function update_sold_quantity_on_order_complete($order_id)
    {
        $this->update_sold_quantity_for_order($order_id);
    }

    /**
     * Update sold quantity when payment is completed
     */
    public function update_sold_quantity_on_payment_complete($order_id)
    {
        $this->update_sold_quantity_for_order($order_id);
    }

    /**
     * Update sold quantity for order items
     */
    private function update_sold_quantity_for_order($order_id)
    {
        $order = wc_get_order($order_id);

        if (!$order) {
            return;
        }

        // Check if we've already processed this order
        $processed = get_post_meta($order_id, '_fs_sold_quantity_updated', true);
        if ($processed) {
            return;
        }

        global $wpdb;
        $products_table = FS_Database::get_table_name('campaign_products');

        foreach ($order->get_items() as $item) {
            $product_id = $item->get_product_id();
            $quantity = $item->get_quantity();

            // Check if this product is in any active flash sale campaigns
            $promotions = $this->get_product_promotions($product_id);

            if (empty($promotions)) {
                continue;
            }

            // Only update for flash sale campaigns, not quantity promotions
            $flash_sale_promotions = array_filter($promotions, function ($promotion) {
                return $promotion['campaign']->type !== 'quantity-promotion';
            });

            if (empty($flash_sale_promotions)) {
                continue;
            }

            // Update sold quantity for each flash sale campaign this product is in
            foreach ($flash_sale_promotions as $promotion) {
                $campaign_id = $promotion['campaign']->id;

                // Update sold quantity in database
                $result = $wpdb->query($wpdb->prepare(
                    "UPDATE {$products_table}
                     SET sold = sold + %d
                     WHERE promotion_id = %d AND product_id = %d",
                    $quantity,
                    $campaign_id,
                    $product_id
                ));

                if ($result !== false) {
                    error_log("FS: Updated sold quantity for product {$product_id} in campaign {$campaign_id}: +{$quantity}");

                    // Clear cache for this product
                    self::clear_product_promotions_cache($product_id);
                } else {
                    error_log("FS: Failed to update sold quantity for product {$product_id} in campaign {$campaign_id}: " . $wpdb->last_error);
                }
            }
        }

        // Mark this order as processed
        update_post_meta($order_id, '_fs_sold_quantity_updated', true);

        // Clear all caches since sold quantities changed
        self::clear_all_caches();
    }

    /**
     * Get sale price for product
     */
    public function get_sale_price($sale_price, $product)
    {
        if (!$product || !is_object($product)) {
            return $sale_price;
        }

        $product_id = $product->get_id();
        $promotions = $this->get_product_promotions($product_id);

        if (empty($promotions)) {
            return $sale_price;
        }

        // Only apply flash sale prices, not quantity promotions
        $flash_sale_promotions = array_filter($promotions, function ($promotion) {
            return $promotion['campaign']->type !== 'quantity-promotion';
        });

        if (empty($flash_sale_promotions)) {
            return $sale_price;
        }

        // Get the best flash sale promotion for this product
        $best_promotion = $this->get_best_promotion($flash_sale_promotions, $product);

        if (!$best_promotion) {
            return $sale_price;
        }

        // Calculate discounted price từ regular price
        $regular_price = floatval($product->get_regular_price());
        $flashsale_price = $this->calculate_discounted_price_from_product_data($regular_price, $best_promotion['product_data']);

        // Nếu có sale price từ WooCommerce, so sánh và chọn giá tốt nhất
        if ($sale_price && floatval($sale_price) > 0) {
            $current_sale_price = floatval($sale_price);
            // Chỉ áp dụng flashsale nếu tốt hơn sale price hiện tại
            if ($flashsale_price < $current_sale_price && $flashsale_price < $regular_price) {
                return $flashsale_price;
            }
            return $sale_price;
        }

        // Nếu không có sale price, áp dụng flashsale nếu có discount
        if ($flashsale_price < $regular_price) {
            return $flashsale_price;
        }

        return $sale_price;
    }

    /**
     * Get flashsale price for product (used in cart)
     */
    public function get_flashsale_price($price, $product)
    {
        if (!$product || !is_object($product)) {
            return $price;
        }

        // Kiểm tra xem có addon khác đã modify giá chưa
        // Nếu giá hiện tại khác với regular price, có thể addon khác đã áp dụng
        $regular_price = floatval($product->get_regular_price());
        $current_price = floatval($price);

        // Nếu giá hiện tại đã được giảm bởi addon khác, chỉ áp dụng flashsale nếu tốt hơn
        $product_id = $product->get_id();
        $promotions = $this->get_product_promotions($product_id);

        if (empty($promotions)) {
            return $price;
        }

        // Only apply flash sale prices, not quantity promotions (they handle their own pricing)
        $flash_sale_promotions = array_filter($promotions, function ($promotion) {
            return $promotion['campaign']->type !== 'quantity-promotion';
        });

        if (empty($flash_sale_promotions)) {
            return $price;
        }

        // Get the best flash sale promotion for this product
        $best_promotion = $this->get_best_promotion($flash_sale_promotions, $product);

        if (!$best_promotion) {
            return $price;
        }

        // Calculate discounted price từ regular price
        $flashsale_price = $this->calculate_discounted_price_from_product_data($regular_price, $best_promotion['product_data']);

        // Chỉ áp dụng flashsale nếu tốt hơn giá hiện tại
        if ($flashsale_price < $current_price && $flashsale_price < $regular_price) {
            return $flashsale_price;
        }

        return $price;
    }

    /**
     * Apply flashsale prices to cart items
     */
    public function apply_flashsale_cart_price($cart)
    {
        if (is_admin() && !defined('DOING_AJAX')) {
            return;
        }

        foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
            $product = $cart_item['data'];
            $product_id = $product->get_id();

            // Skip if this is a gift item
            if (isset($cart_item['fs_gift']) && $cart_item['fs_gift']) {
                continue;
            }

            // Skip if other addon has already modified this cart item
            if (isset($cart_item['fs_addon_modified']) && $cart_item['fs_addon_modified']) {
                continue;
            }

            $promotions = $this->get_product_promotions($product_id);

            if (empty($promotions)) {
                continue;
            }

            // Only apply flash sale prices, not quantity promotions (they handle their own cart pricing)
            $flash_sale_promotions = array_filter($promotions, function ($promotion) {
                return $promotion['campaign']->type !== 'quantity-promotion';
            });

            if (empty($flash_sale_promotions)) {
                continue;
            }

            // Get the best flash sale promotion for this product
            $best_promotion = $this->get_best_promotion($flash_sale_promotions, $product);

            if (!$best_promotion) {
                continue;
            }

            // Calculate discounted price từ regular price
            $regular_price = floatval($product->get_regular_price());
            $current_price = floatval($product->get_price());
            $flashsale_price = $this->calculate_discounted_price_from_product_data($regular_price, $best_promotion['product_data']);

            // Chỉ áp dụng nếu flashsale tốt hơn giá hiện tại
            if ($flashsale_price < $current_price && $flashsale_price < $regular_price) {
                $product->set_price($flashsale_price);
                // Mark as modified by flashsale
                $cart_item['fs_flashsale_applied'] = true;
            }
        }
    }

    /**
     * Get best promotion for product
     * Priority logic: Higher priority number wins (5 > 4)
     * If same priority, choose better price
     */
    private function get_best_promotion($promotions, $product)
    {
        if (empty($promotions)) {
            return null;
        }

        $best_promotion = null;
        $best_priority = -1;
        $best_price = $product->get_regular_price();

        foreach ($promotions as $promotion) {
            $campaign_priority = intval($promotion['campaign']->priority ?? 0);
            $discounted_price = $this->calculate_discounted_price_from_product_data($product->get_regular_price(), $promotion['product_data']);

            // Priority logic: Higher priority number wins
            if ($campaign_priority > $best_priority) {
                $best_promotion = $promotion;
                $best_priority = $campaign_priority;
                $best_price = $discounted_price;
            }
            // If same priority, choose better price
            elseif ($campaign_priority == $best_priority && $discounted_price < $best_price) {
                $best_promotion = $promotion;
                $best_price = $discounted_price;
            }
        }

        return $best_promotion;
    }

    /**
     * Calculate discounted price
     */
    private function calculate_discounted_price($original_price, $promotion)
    {
        $campaign = $promotion['campaign'];
        $target = $promotion['target'];

        // Get global settings for discount rules
        $global_settings = json_decode($campaign->global_settings, true);

        if (empty($global_settings)) {
            return $original_price;
        }

        $discount_type = $global_settings['discount_type'] ?? 'percentage';
        $discount_value = floatval($global_settings['discount_value'] ?? 0);

        if ($discount_value <= 0) {
            return $original_price;
        }

        if ($discount_type === 'percentage') {
            $discounted_price = $original_price * (1 - $discount_value / 100);
        } else {
            $discounted_price = $original_price - $discount_value;
        }

        // Apply max discount limit if set
        if (isset($global_settings['max_discount_amount']) && $global_settings['max_discount_amount'] > 0) {
            $max_discount = floatval($global_settings['max_discount_amount']);
            $actual_discount = $original_price - $discounted_price;

            if ($actual_discount > $max_discount) {
                $discounted_price = $original_price - $max_discount;
            }
        }

        return max(0, $discounted_price);
    }

    /**
     * Calculate discounted price from product data
     */
    private function calculate_discounted_price_from_product_data($original_price, $product_data)
    {
        $discount_type = $product_data->discount_type ?? 2; // 1=percentage, 2=fixed_amount
        $discount_value = floatval($product_data->discount_value ?? 0);

        if ($discount_value <= 0) {
            return $original_price;
        }

        if ($discount_type == 1) { // percentage
            $discounted_price = $original_price * (1 - $discount_value / 100);
        } else { // fixed amount
            $discounted_price = $original_price - $discount_value;
        }

        // Apply max discount limit if set
        if (isset($product_data->max_discount_amount) && $product_data->max_discount_amount > 0) {
            $max_discount = floatval($product_data->max_discount_amount);
            $actual_discount = $original_price - $discounted_price;

            if ($actual_discount > $max_discount) {
                $discounted_price = $original_price - $max_discount;
            }
        }

        return max(0, $discounted_price);
    }

    /**
     * Get cached active campaigns
     */
    private function get_cached_active_campaigns()
    {
        if (self::$active_campaigns === null) {
            self::$active_campaigns = FS()->campaigns()->get_active_campaigns();
        }
        return self::$active_campaigns;
    }

    /**
     * Clear campaigns cache (useful for testing or when campaigns are updated)
     */
    public static function clear_campaigns_cache()
    {
        self::$active_campaigns = null;
    }

    /**
     * Clear product promotions cache (useful for testing or when campaigns are updated)
     */
    public static function clear_product_promotions_cache($product_id = null)
    {
        if ($product_id !== null) {
            // Clear cache for specific product
            unset(self::$product_promotions_cache[$product_id]);
        } else {
            // Clear all product promotions cache
            self::$product_promotions_cache = [];
        }
    }

    /**
     * Clear all caches
     */
    public static function clear_all_caches()
    {
        self::clear_campaigns_cache();
        self::clear_product_promotions_cache();
    }

    /**
     * Get product promotions
     */
    private function get_product_promotions($product_id)
    {
        // Check if we already have cached promotions for this product
        if (isset(self::$product_promotions_cache[$product_id])) {
            return self::$product_promotions_cache[$product_id];
        }

        // Use cached campaigns to avoid duplicate queries
        $campaigns = $this->get_cached_active_campaigns();
        $promotions = [];
        $seen_campaigns = []; // Track campaigns we've already processed for this product

        foreach ($campaigns as $campaign) {
            // Skip if we've already processed this campaign for this product
            if (isset($seen_campaigns[$campaign->id])) {
                continue;
            }

            // Get campaign products instead of targets
            $campaign_products = FS()->campaigns()->get_campaign_products($campaign->id);

            // Find the best product record for this product_id in this campaign
            $best_product_record = null;
            $best_discount_value = 0;

            foreach ($campaign_products as $campaign_product) {
                if ($campaign_product->product_id == $product_id) {
                    // Choose the record with highest discount value (best deal)
                    $current_discount = floatval($campaign_product->discount_value ?? 0);
                    if ($best_product_record === null || $current_discount > $best_discount_value) {
                        $best_product_record = $campaign_product;
                        $best_discount_value = $current_discount;
                    }
                }
            }

            // Only add if we found a product record for this campaign
            if ($best_product_record !== null) {
                // Check if product is within its specific date range (if set)
                $effective_start_date = !empty($best_product_record->start_date) ? $best_product_record->start_date : $campaign->start_date;
                $effective_end_date = !empty($best_product_record->end_date) ? $best_product_record->end_date : $campaign->end_date;

                // Check if current time is within the effective date range
                $current_time = current_time('mysql');
                $is_active = true;

                if (!empty($effective_start_date) && $current_time < $effective_start_date) {
                    $is_active = false;
                }

                if (!empty($effective_end_date) && $current_time > $effective_end_date) {
                    $is_active = false;
                }

                // Only include active promotions
                if (!$is_active) {
                    continue;
                }

                // Mark this campaign as seen for this product
                $seen_campaigns[$campaign->id] = true;

                // Create a target-like object from product data for compatibility
                // For quantity promotions, don't use qty_max as stock limit since it stores min_quantity
                $qty_max = 0;
                $sold = 0;

                // Only set stock data for flash sale campaigns, not quantity promotions
                if ($campaign->type !== 'quantity-promotion') {
                    $qty_max = isset($best_product_record->qty_max) ? $best_product_record->qty_max : 0;
                    $sold = isset($best_product_record->sold) ? $best_product_record->sold : 0;

                    // Check if sold quantity has reached max quantity limit
                    if ($qty_max > 0 && $sold >= $qty_max) {
                        // Skip this promotion - sold out
                        continue;
                    }
                }

                $target = (object) [
                    'pricing_rules' => [
                        'qty_max' => $qty_max,
                        'sold' => $sold,
                        'discount_type' => isset($best_product_record->discount_type) ? $best_product_record->discount_type : 1,
                        'discount_value' => isset($best_product_record->discount_value) ? $best_product_record->discount_value : 0,
                        'effective_start_date' => $effective_start_date,
                        'effective_end_date' => $effective_end_date
                    ]
                ];

                $promotions[] = [
                    'campaign' => $campaign,
                    'product_data' => $best_product_record,
                    'target' => $target
                ];
            }
        }

        // Cache the result for this product
        self::$product_promotions_cache[$product_id] = $promotions;

        return $promotions;
    }

    /**
     * Calculate promotion price
     */
    private function calculate_promotion_price($product, $promotions)
    {
        $best_price = $product->get_regular_price();

        foreach ($promotions as $promotion) {
            $target = $promotion['target'];
            $pricing_rules = $target->pricing_rules;

            if (!$pricing_rules || !isset($pricing_rules['discount_type'])) {
                continue;
            }

            $discount_type = $pricing_rules['discount_type'];
            $discount_value = floatval($pricing_rules['discount_value']);

            if ($discount_type === 'percentage') {
                $promotion_price = $product->get_regular_price() * (1 - $discount_value / 100);
            } else {
                $promotion_price = $product->get_regular_price() - $discount_value;
            }

            // Apply max discount limit if set
            if (isset($pricing_rules['max_discount_amount']) && $pricing_rules['max_discount_amount'] > 0) {
                $max_discount = floatval($pricing_rules['max_discount_amount']);
                $actual_discount = $product->get_regular_price() - $promotion_price;

                if ($actual_discount > $max_discount) {
                    $promotion_price = $product->get_regular_price() - $max_discount;
                }
            }

            $best_price = min($best_price, max(0, $promotion_price));
        }

        return $best_price;
    }

    /**
     * Render promotion info
     */
    private function render_promotion_info($promotion, $product)
    {
        $campaign = $promotion['campaign'];
        $target = isset($promotion['target']) ? $promotion['target'] : null;

        echo '<div class="fs-promotion-item" data-campaign-id="' . esc_attr($campaign->id) . '">';

        // Promotion title
        echo '<h4 class="fs-promotion-title">' . esc_html($campaign->name) . '</h4>';

        // Promotion description
        if ($campaign->description) {
            echo '<p class="fs-promotion-description">' . esc_html($campaign->description) . '</p>';
        }

        // Countdown timer - use product-specific end date if available, otherwise campaign end date
        $product_data = isset($promotion['product_data']) ? $promotion['product_data'] : null;
        $effective_end_date = ($product_data && !empty($product_data->end_date)) ? $product_data->end_date : $campaign->end_date;

        if ($effective_end_date) {
            $this->render_countdown_timer($effective_end_date);
        }

        // Stock progress if applicable (only show for flash sale campaigns, not quantity promotions)
        if ($campaign->type !== 'quantity-promotion') {
            $this->render_stock_progress($target, $product);
        }

        // Gifts if any
        $this->render_promotion_gifts($campaign->id);

        echo '</div>';
    }

    /**
     * Render countdown timer
     */
    private function render_countdown_timer($end_date)
    {
        $end_timestamp = strtotime($end_date);
        $current_timestamp = current_time('timestamp');

        if ($end_timestamp <= $current_timestamp) {
            return;
        }

        echo '<div class="fs-countdown-timer" data-end-time="' . esc_attr($end_timestamp) . '">';
        echo '<span class="fs-countdown-label">' . __('Ends in:', 'flashsale-core') . '</span>';
        echo '<div class="fs-countdown-display">';
        echo '<span class="fs-countdown-days">00</span>d ';
        echo '<span class="fs-countdown-hours">00</span>h ';
        echo '<span class="fs-countdown-minutes">00</span>m ';
        echo '<span class="fs-countdown-seconds">00</span>s';
        echo '</div>';
        echo '</div>';
    }

    /**
     * Render stock progress
     */
    private function render_stock_progress($target, $product)
    {
        // Check if target exists and has pricing_rules
        if (!$target || !isset($target->pricing_rules) || !is_array($target->pricing_rules)) {
            return;
        }

        $pricing_rules = $target->pricing_rules;

        if (!isset($pricing_rules['qty_max']) || $pricing_rules['qty_max'] <= 0) {
            return;
        }

        $max_qty = intval($pricing_rules['qty_max']);
        $sold_qty = intval($pricing_rules['sold'] ?? 0);
        $remaining_qty = max(0, $max_qty - $sold_qty);
        $progress_percent = $max_qty > 0 ? ($sold_qty / $max_qty) * 100 : 0;

        echo '<div class="fs-stock-progress">';
        echo '<div class="fs-stock-info">';
        echo '<span class="fs-stock-sold">' . sprintf(__('Sold: %d', 'flashsale-core'), $sold_qty) . '</span>';
        echo '<span class="fs-stock-remaining">' . sprintf(__('Remaining: %d', 'flashsale-core'), $remaining_qty) . '</span>';
        echo '</div>';
        echo '<div class="fs-progress-bar">';
        echo '<div class="fs-progress-fill" style="width: ' . esc_attr($progress_percent) . '%"></div>';
        echo '</div>';
        echo '</div>';
    }

    /**
     * Render promotion gifts
     */
    private function render_promotion_gifts($campaign_id)
    {
        $gifts = FS()->campaigns()->get_campaign_gifts($campaign_id);

        if (empty($gifts)) {
            return;
        }

        echo '<div class="fs-promotion-gifts">';
        echo '<h5 class="fs-gifts-title">' . __('Free Gifts:', 'flashsale-core') . '</h5>';
        echo '<div class="fs-gifts-list">';

        foreach ($gifts as $gift) {
            echo '<div class="fs-gift-item">';

            if ($gift->gift_type === 'product' && $gift->gift_id) {
                $gift_product = wc_get_product($gift->gift_id);
                if ($gift_product) {
                    echo '<span class="fs-gift-name">' . esc_html($gift_product->get_name()) . '</span>';
                }
            } else {
                echo '<span class="fs-gift-name">' . esc_html($gift->gift_name) . '</span>';
            }

            echo '</div>';
        }

        echo '</div>';
        echo '</div>';
    }

    /**
     * Campaign shortcode
     */
    public function campaign_shortcode($atts)
    {
        $atts = shortcode_atts([
            'id' => 0,
            'type' => '',
            'limit' => 10,
            'columns' => 4
        ], $atts);

        $campaign_id = intval($atts['id']);

        if (!$campaign_id) {
            return '';
        }

        $campaign = FS()->campaigns()->get_campaign($campaign_id);

        if (!$campaign || !$campaign->status) {
            return '';
        }

        ob_start();
        $this->render_campaign_shortcode($campaign, $atts);
        return ob_get_clean();
    }

    /**
     * Render campaign shortcode
     */
    private function render_campaign_shortcode($campaign, $atts)
    {
        $targets = FS()->campaigns()->get_campaign_targets($campaign->id);
        $limit = intval($atts['limit']);
        $columns = intval($atts['columns']);

        $end_time = $campaign->end_date; // ví dụ: "2025-06-30 16:18:00"
        $start_time = $campaign->start_date; // ví dụ: "2025-06-30 16:18:00"


        $diff = $timestamp - $now;
        if (empty($targets)) {
            return;
        }
        echo '<section class="home-5 section-40 bg-bg">';
        echo '<div class="container">';
        echo '<div class="fs-campaign-shortcode home-5-content bg-Neutral-White shadow-Dropshadow-Box rounded-[32px] lg:p-10 p-6 w-full xl:rem:w-[1480px] mx-auto" data-campaign-id="' . esc_attr($campaign->id) . '">';
        echo '<div class="heading flex items-center mb-6 gap-8">';
        echo '<h3 class="rem:text-[36px] font-secondary font-bold text-Primary-Orange">' . esc_html($campaign->name) . '</h3>';
        echo
        '<div class="countdown" data-start="' . $start_time . '" data-end="' . $end_time . '">
       
         </div>';

        echo '</div>';

        echo '<div class="fs-products-grid fs-columns-' . esc_attr($columns) . '">';

        $count = 0;
        foreach ($targets as $target) {
            if ($count >= $limit) {
                break;
            }

            if ($target->target_type === 'product') {
                $product = wc_get_product($target->target_id);
                if ($product) {
                    $this->render_campaign_product($product, $campaign, $target);
                    $count++;
                }
            }
        }

        echo '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';
    }

    /**
     * Render campaign product
     */
    private function render_campaign_product($product, $campaign, $target)
    {
        $pricing_rules = $target->pricing_rules;

        $sold_quantity = (int) ($pricing_rules['sold_quantity'] ?? 0);
        $max_quantity  = (int) ($pricing_rules['max_quantity'] ?? 0);

        if ($max_quantity > 0) {
            $percent = round(($sold_quantity / $max_quantity) * 100);
        } else {
            $percent = 100;
        }


?>
        <div class="product-item group">
            <div class="image rounded-2xl border border-Neutral-100 group relative overflow-hidden">
                <a class="img-ratio ratio:pt-[360_320] rounded-2xl zoom-img" href="<?php echo esc_url($product->get_permalink()); ?>">
                    <?php echo $product->get_image('medium'); ?>
                </a>
                <div class="button-add-cart absolute flex justify-center bottom-0 left-0 w-full translate-y-full opacity-0 group-hover:-translate-y-10 group-hover:opacity-100 transition-all duration-500 z-10">
                    <a class="btn-add-cart wvs-add-to-cart-button" href="<?php echo esc_url($product->get_permalink()); ?>" data-product-id="<?php echo $product->get_id(); ?>" data-product-variation-id="<?php echo $id_variable; ?>">
                        <span><?php echo __('THÊM VÀO GIỎ HÀNG', 'benty'); ?></span>
                        <div class=" icon"> <i class="fa-regular fa-cart-flatbed-boxes"></i>
                        </div>
                    </a>
                </div>
            </div>
            <div class="content mt-4">
                <div class="content-info">
                    <h3 class="text-lg font-normal text-Neutral-Body mb-2 group-hover:text-Primary-Red">
                        <a href="<?php echo esc_url($product->get_permalink()); ?>"><?php echo esc_html($product->get_name()); ?></a>
                    </h3>
                    <div class="product-price"><?php echo $product->get_price_html(); ?></div>
                    <div class="sell-line" data-percent="<?php echo $percent ?>"> <span>Bán chạy</span></div>
                </div>
            </div>
        </div>
<?php
    }
}
