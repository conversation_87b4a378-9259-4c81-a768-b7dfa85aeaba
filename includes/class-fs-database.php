<?php
/**
 * Database management for FlashSale Core
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FS_Database {
    
    /**
     * Database version
     */
    const DB_VERSION = '2.1.0';
    
    /**
     * Table names for Flash Sale only
     */
    public static $tables = [
        'campaigns' => 'fs_campaigns',
        'campaign_products' => 'fs_campaign_products',
        'campaign_targets' => 'fs_campaign_targets',
        'campaign_gifts' => 'fs_campaign_gifts',
        'campaign_rules' => 'fs_campaign_rules',
        'product_gifts' => 'fs_product_gifts',
        'product_gifts_offline' => 'fs_product_gifts_offline',
        'addons' => 'fs_addons'
    ];
    
    /**
     * Initialize database
     */
    public function init() {
        add_action('init', [$this, 'check_database_version']);
    }
    
    /**
     * Check if database needs update
     */
    public function check_database_version() {
        $installed_version = get_option('fs_db_version', '0');
        
        if (version_compare($installed_version, self::DB_VERSION, '<')) {
            $this->create_tables();
            $this->migrate_data();
            update_option('fs_db_version', self::DB_VERSION);
        }
    }
    
    /**
     * Create all tables
     */
    public function create_tables() {
        global $wpdb;

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        $charset_collate = $wpdb->get_charset_collate();

        // Flash Sale Campaigns table (focused on flash-sale, extensible for addon types)
        $sql_campaigns = "CREATE TABLE {$wpdb->prefix}fs_campaigns (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            type varchar(50) DEFAULT 'flash-sale' COMMENT 'Core supports flash-sale, addons can extend with other types',
            start_date datetime DEFAULT NULL,
            end_date datetime DEFAULT NULL,
            status tinyint(1) DEFAULT 1,
            priority int(11) DEFAULT 0,
            global_settings longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY status_priority (status, priority),
            KEY date_range (start_date, end_date),
            KEY type (type)
        ) $charset_collate;";

        // Flash Sale Campaign Products table (based on aitfs_promotion_product)
        $sql_campaign_products = "CREATE TABLE {$wpdb->prefix}fs_campaign_products (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            promotion_id mediumint(9) NOT NULL,
            product_id bigint(20) NOT NULL,
            regular_price decimal(10,2) DEFAULT NULL,
            discount_type tinyint(1) DEFAULT 2 COMMENT '1=percentage, 2=fixed_amount',
            discount_value decimal(10,2) DEFAULT NULL,
            max_discount_amount decimal(10,2) DEFAULT NULL,
            sold int(11) DEFAULT 0 COMMENT 'fake sold quantity',
            qty_max int(11) DEFAULT NULL COMMENT 'max quantity per customer',
            percent_sold_max int(11) DEFAULT NULL COMMENT 'deal quantity limit',
            start_date datetime DEFAULT NULL,
            end_date datetime DEFAULT NULL,
            sku varchar(100) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY promotion_id (promotion_id),
            KEY product_id (product_id),
            KEY date_range (start_date, end_date)
        ) $charset_collate;";
        
        // Flash Sale Product Gifts table (based on aitfs_promotion_gift)
        $sql_product_gifts = "CREATE TABLE {$wpdb->prefix}fs_product_gifts (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            promotion_id mediumint(9) NOT NULL,
            current_gift_id bigint(20) NOT NULL COMMENT 'product that receives gift',
            product_id bigint(20) NOT NULL COMMENT 'gift product',
            gift_quantity int(11) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY promotion_gift (promotion_id, current_gift_id),
            KEY product_id (product_id)
        ) $charset_collate;";

        // Flash Sale Product Gifts Offline table (based on aitfs_promotion_gift_offline)
        $sql_product_gifts_offline = "CREATE TABLE {$wpdb->prefix}fs_product_gifts_offline (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            promotion_id mediumint(9) NOT NULL,
            current_gift_id bigint(20) NOT NULL COMMENT 'product that receives gift',
            description text,
            image_url varchar(500) DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY promotion_gift (promotion_id, current_gift_id)
        ) $charset_collate;";
        
        // Campaign geography table
        $sql_geography = "CREATE TABLE {$wpdb->prefix}fs_campaign_geography (
            id int(11) NOT NULL AUTO_INCREMENT,
            campaign_id int(11) NOT NULL,
            rule_id int(11),
            geo_type varchar(50) NOT NULL,
            geo_code varchar(100) NOT NULL,
            geo_name varchar(255),
            geo_config json,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_campaign (campaign_id),
            KEY idx_rule (rule_id),
            KEY idx_geo (geo_type, geo_code)
        ) $charset_collate;";
        
        // Campaign stats table
        $sql_stats = "CREATE TABLE {$wpdb->prefix}fs_campaign_stats (
            id int(11) NOT NULL AUTO_INCREMENT,
            campaign_id int(11) NOT NULL,
            target_id int(11),
            target_type varchar(100),
            sold_count int(11) DEFAULT 0,
            real_sold_count int(11) DEFAULT 0,
            revenue decimal(10,2) DEFAULT 0,
            additional_stats json,
            last_updated timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_campaign_target (campaign_id, target_id, target_type),
            KEY idx_campaign (campaign_id)
        ) $charset_collate;";
        
        // Addons table
        $sql_addons = "CREATE TABLE {$wpdb->prefix}fs_addons (
            id int(11) NOT NULL AUTO_INCREMENT,
            addon_name varchar(255) NOT NULL,
            addon_slug varchar(100) NOT NULL,
            addon_version varchar(20),
            addon_type varchar(100),
            status tinyint(1) DEFAULT 1,
            settings json,
            dependencies json,
            activated_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_slug (addon_slug),
            KEY idx_status (status),
            KEY idx_type (addon_type)
        ) $charset_collate;";
        
        // Campaign targets table
        $sql_campaign_targets = "CREATE TABLE {$wpdb->prefix}fs_campaign_targets (
            id int(11) NOT NULL AUTO_INCREMENT,
            campaign_id int(11) NOT NULL,
            rule_id int(11),
            target_type varchar(100) NOT NULL,
            target_id bigint(20) NOT NULL,
            target_name varchar(255),
            target_config json,
            pricing_rules json,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_campaign (campaign_id),
            KEY idx_rule (rule_id),
            KEY idx_target (target_type, target_id)
        ) $charset_collate;";

        // Campaign gifts table
        $sql_campaign_gifts = "CREATE TABLE {$wpdb->prefix}fs_campaign_gifts (
            id int(11) NOT NULL AUTO_INCREMENT,
            campaign_id int(11) NOT NULL,
            rule_id int(11),
            target_id int(11),
            gift_type varchar(100) NOT NULL,
            gift_id bigint(20),
            gift_name varchar(255),
            gift_config json,
            allow_selection tinyint(1) DEFAULT 0,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_campaign (campaign_id),
            KEY idx_rule (rule_id),
            KEY idx_target (target_id),
            KEY idx_gift (gift_type, gift_id)
        ) $charset_collate;";

        // Campaign rules table
        $sql_campaign_rules = "CREATE TABLE {$wpdb->prefix}fs_campaign_rules (
            id int(11) NOT NULL AUTO_INCREMENT,
            campaign_id int(11) NOT NULL,
            rule_type varchar(100) NOT NULL,
            rule_name varchar(255),
            conditions json,
            actions json,
            priority int(11) DEFAULT 0,
            status tinyint(1) DEFAULT 1,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_campaign (campaign_id),
            KEY idx_type (rule_type),
            KEY idx_status_priority (status, priority)
        ) $charset_collate;";

        // Addon hooks table
        $sql_addon_hooks = "CREATE TABLE {$wpdb->prefix}fs_addon_hooks (
            id int(11) NOT NULL AUTO_INCREMENT,
            addon_id int(11) NOT NULL,
            hook_name varchar(255) NOT NULL,
            hook_type varchar(100) NOT NULL,
            callback_class varchar(255),
            callback_method varchar(255),
            priority int(11) DEFAULT 10,
            status tinyint(1) DEFAULT 1,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_addon (addon_id),
            KEY idx_hook (hook_name),
            KEY idx_status (status)
        ) $charset_collate;";
        
        // Execute table creation
        $results = [];
        $results['campaigns'] = dbDelta($sql_campaigns);
        $results['campaign_products'] = dbDelta($sql_campaign_products);
        $results['campaign_targets'] = dbDelta($sql_campaign_targets);
        $results['campaign_gifts'] = dbDelta($sql_campaign_gifts);
        $results['campaign_rules'] = dbDelta($sql_campaign_rules);
        $results['product_gifts'] = dbDelta($sql_product_gifts);
        $results['product_gifts_offline'] = dbDelta($sql_product_gifts_offline);
        $results['geography'] = dbDelta($sql_geography);
        $results['stats'] = dbDelta($sql_stats);
        $results['addons'] = dbDelta($sql_addons);
        $results['addon_hooks'] = dbDelta($sql_addon_hooks);

        // Log results for debugging
        error_log('FlashSale Core: Database tables creation results: ' . print_r($results, true));

        return $results;
    }

    /**
     * Force create tables (for debugging)
     */
    public function force_create_tables() {
        // Reset database version to force recreation
        delete_option('fs_db_version');
        delete_option('fs_migration_completed');

        return $this->create_tables();
    }
    
    /**
     * Get table name with prefix
     */
    public static function get_table_name($table) {
        global $wpdb;

        // Check if table exists in our tables array
        if (!isset(self::$tables[$table])) {
            // Return a safe fallback or throw error
            error_log("FlashSale Core: Table '$table' not found in tables array");
            return $wpdb->prefix . 'fs_' . $table; // fallback
        }

        return $wpdb->prefix . self::$tables[$table];
    }
    
    /**
     * Migrate data from old schema
     */
    public function migrate_data() {
        // Check if migration is needed
        if (get_option('fs_migration_completed', false)) {
            return;
        }

        $this->migrate_campaigns();
        $this->migrate_products();
        $this->migrate_gifts();

        update_option('fs_migration_completed', true);
    }

    /**
     * Migrate campaigns from old structure
     */
    private function migrate_campaigns() {
        global $wpdb;

        $old_table = $wpdb->prefix . 'aitfs_promotions';
        $new_table = self::get_table_name('campaigns');

        // Check if old table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$old_table'") != $old_table) {
            return;
        }

        // Get campaigns from old table
        $old_campaigns = $wpdb->get_results("SELECT * FROM $old_table");

        foreach ($old_campaigns as $campaign) {
            // Convert old campaign to new structure
            $new_campaign = [
                'name' => $campaign->promotion_name ?? $campaign->name ?? 'Migrated Campaign',
                'description' => $campaign->promotion_description ?? $campaign->description ?? '',
                'type' => $campaign->promotion_type ?? $campaign->type ?? 'flash-sale',
                'priority' => $campaign->priority ?? 0,
                'status' => $campaign->campaign_status ?? $campaign->status ?? 1,
                'start_date' => $campaign->start_date,
                'end_date' => $campaign->end_date,
                'global_settings' => $campaign->data_json ?? $campaign->global_settings ?? '{}',
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ];

            $result = $wpdb->insert($new_table, $new_campaign);

            if ($result) {
                $new_campaign_id = $wpdb->insert_id;
                $this->migrate_campaign_products($campaign->id, $new_campaign_id);
            }
        }
    }

    /**
     * Migrate products from old structure
     */
    private function migrate_products() {
        // This will be called from migrate_campaigns for each campaign
    }

    /**
     * Migrate campaign products
     */
    private function migrate_campaign_products($old_campaign_id, $new_campaign_id) {
        global $wpdb;

        $old_products_table = $wpdb->prefix . 'aitfs_promotion_product';
        $products_table = self::get_table_name('campaign_products');

        // Get products from old table
        $old_products = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $old_products_table WHERE promotion_id = %d",
            $old_campaign_id
        ));

        foreach ($old_products as $product) {
            $new_product = [
                'promotion_id' => $new_campaign_id,
                'product_id' => $product->product_id,
                'regular_price' => $product->regular_price ?? null,
                'discount_type' => $product->discount_type ?? 2,
                'discount_value' => $product->discount_value ?? null,
                'max_discount_amount' => $product->max_discount_amount ?? null,
                'sold' => $product->sold ?? 0,
                'qty_max' => $product->qty_max ?? null,
                'percent_sold_max' => $product->percent_sold_max ?? null,
                'start_date' => $product->start_date ?? null,
                'end_date' => $product->end_date ?? null,
                'sku' => $product->sku ?? null,
                'created_at' => current_time('mysql')
            ];

            $wpdb->insert($products_table, $new_product);
        }
    }

    /**
     * Migrate gifts from old structure
     */
    private function migrate_gifts() {
        global $wpdb;

        $old_gifts_table = $wpdb->prefix . 'aitfs_promotion_gift';
        $old_gifts_offline_table = $wpdb->prefix . 'aitfs_promotion_gift_offline';
        $old_category_gifts_table = $wpdb->prefix . 'aitfs_promotion_category_gift';
        $old_category_gifts_offline_table = $wpdb->prefix . 'aitfs_promotion_category_gift_offline';

        $new_gifts_table = self::get_table_name('product_gifts');

        // Migrate product gifts
        if ($wpdb->get_var("SHOW TABLES LIKE '$old_gifts_table'") == $old_gifts_table) {
            $old_gifts = $wpdb->get_results("SELECT * FROM $old_gifts_table");

            foreach ($old_gifts as $gift) {
                $new_gift = [
                    'campaign_id' => $gift->promotion_id,
                    'gift_type' => 'product',
                    'gift_id' => $gift->current_gift_id,
                    'gift_name' => $gift->name,
                    'gift_config' => json_encode(['product_id' => $gift->product_id]),
                    'allow_selection' => $gift->allow_gift_selection,
                    'created_at' => $gift->created_at,
                    'updated_at' => $gift->updated_at
                ];

                $wpdb->insert($new_gifts_table, $new_gift);
            }
        }

        // Migrate offline gifts
        if ($wpdb->get_var("SHOW TABLES LIKE '$old_gifts_offline_table'") == $old_gifts_offline_table) {
            $old_gifts_offline = $wpdb->get_results("SELECT * FROM $old_gifts_offline_table");

            foreach ($old_gifts_offline as $gift) {
                $new_gift = [
                    'campaign_id' => $gift->promotion_id,
                    'gift_type' => 'offline',
                    'gift_id' => $gift->current_gift_id,
                    'gift_config' => json_encode([
                        'product_id' => $gift->product_id,
                        'image' => $gift->image,
                        'description' => $gift->description
                    ]),
                    'allow_selection' => 0,
                    'created_at' => $gift->created_at,
                    'updated_at' => $gift->updated_at
                ];

                $wpdb->insert($new_gifts_table, $new_gift);
            }
        }
    }
}
