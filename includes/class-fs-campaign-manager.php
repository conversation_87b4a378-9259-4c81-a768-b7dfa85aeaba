<?php
/**
 * Campaign Manager for FlashSale Core
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

class FS_Campaign_Manager {
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('wp_loaded', [$this, 'load_active_campaigns']);
    }
    
    /**
     * Load active campaigns
     */
    public function load_active_campaigns() {
        // Load campaigns that are currently active
        $this->active_campaigns = $this->get_active_campaigns();
    }
    
    /**
     * Get active campaigns
     */
    public function get_active_campaigns() {
        global $wpdb;

        $table = FS_Database::get_table_name('campaigns');
        $current_time = current_time('mysql');

        $campaigns = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM $table
            WHERE status = 1
            AND (start_date IS NULL OR start_date <= %s)
            AND (end_date IS NULL OR end_date >= %s)
            ORDER BY priority DESC, id ASC
        ", $current_time, $current_time));

        return $campaigns;
    }

    /**
     * Get all campaigns (active and inactive)
     */
    public function get_all_campaigns() {
        global $wpdb;

        $table = FS_Database::get_table_name('campaigns');

        $campaigns = $wpdb->get_results("
            SELECT * FROM $table
            ORDER BY created_at DESC, priority DESC
        ");

        return $campaigns;
    }
    
    /**
     * Get campaign by ID
     */
    public function get_campaign($campaign_id) {
        global $wpdb;
        
        $table = FS_Database::get_table_name('campaigns');
        
        $campaign = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM $table WHERE id = %d
        ", $campaign_id));
        
        if ($campaign) {
            // Load campaign targets
            $campaign->targets = $this->get_campaign_targets($campaign_id);
            
            // Load campaign gifts
            $campaign->gifts = $this->get_campaign_gifts($campaign_id);
            
            // Load campaign rules
            $campaign->rules = $this->get_campaign_rules($campaign_id);
        }
        
        return $campaign;
    }
    
    /**
     * Get campaign targets
     */
    public function get_campaign_targets($campaign_id) {
        global $wpdb;

        $table = FS_Database::get_table_name('campaign_targets');

        $targets = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM $table WHERE campaign_id = %d
        ", $campaign_id));

        // If no targets found, fallback to campaign_products for backward compatibility
        if (empty($targets)) {
            $products_table = FS_Database::get_table_name('campaign_products');
            $products = $wpdb->get_results($wpdb->prepare("
                SELECT * FROM $products_table WHERE promotion_id = %d
            ", $campaign_id));

            // Convert products to targets format
            $targets = [];
            foreach ($products as $product) {
                $target = new stdClass();
                $target->id = $product->id;
                $target->campaign_id = $campaign_id;
                $target->rule_id = null;
                $target->target_type = 'product';
                $target->target_id = $product->product_id;
                $target->target_name = get_the_title($product->product_id);
                $target->target_config = [];
                $target->pricing_rules = [
                    'discount_type' => $product->discount_type == 1 ? 'percentage' : 'fixed_amount',
                    'discount_value' => $product->discount_value,
                    'max_discount_amount' => $product->max_discount_amount,
                    'max_quantity' => $product->qty_max,
                    'sold_quantity' => $product->sold,
                    'start_date' => $product->start_date,
                    'end_date' => $product->end_date
                ];
                $target->created_at = $product->created_at;
                $target->updated_at = $product->created_at;

                $targets[] = $target;
            }
        } else {
            // Decode JSON fields for regular targets
            foreach ($targets as &$target) {
                $target->target_config = json_decode($target->target_config, true);
                $target->pricing_rules = json_decode($target->pricing_rules, true);
            }
        }

        return $targets;
    }
    
    /**
     * Get campaign gifts
     */
    public function get_campaign_gifts($campaign_id) {
        global $wpdb;
        
        $table = FS_Database::get_table_name('campaign_gifts');
        
        $gifts = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM $table WHERE campaign_id = %d
        ", $campaign_id));
        
        // Decode JSON fields
        foreach ($gifts as &$gift) {
            $gift->gift_config = json_decode($gift->gift_config, true);
        }
        
        return $gifts;
    }
    
    /**
     * Get campaign rules
     */
    public function get_campaign_rules($campaign_id) {
        global $wpdb;
        
        $table = FS_Database::get_table_name('campaign_rules');
        
        $rules = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM $table WHERE campaign_id = %d AND status = 1 ORDER BY priority DESC
        ", $campaign_id));
        
        // Decode JSON fields
        foreach ($rules as &$rule) {
            $rule->conditions = json_decode($rule->conditions, true);
            $rule->actions = json_decode($rule->actions, true);
        }
        
        return $rules;
    }

    /**
     * Get campaign products
     */
    public function get_campaign_products($campaign_id) {
        global $wpdb;

        $table = FS_Database::get_table_name('campaign_products');

        $products = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM $table WHERE promotion_id = %d ORDER BY product_id ASC
        ", $campaign_id));

        return $products;
    }

    /**
     * Create new campaign with products
     */
    public function create_campaign($data, $products = []) {
        global $wpdb;

        $table = FS_Database::get_table_name('campaigns');

        $campaign_data = [
            'name' => sanitize_text_field($data['name']),
            'description' => sanitize_textarea_field($data['description']),
            'type' => sanitize_text_field($data['type']),
            'priority' => intval($data['priority']),
            'status' => intval($data['status']),
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'global_settings' => isset($data['global_settings']) ? json_encode($data['global_settings']) : '{}',
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ];

        $wpdb->query('START TRANSACTION');

        try {
            $result = $wpdb->insert($table, $campaign_data);

            if ($result) {
                $campaign_id = $wpdb->insert_id;

                // Create products if provided
                if (!empty($products)) {
                    $this->save_campaign_products($campaign_id, $products);
                }

                // Create targets if provided
                if (!empty($data['targets'])) {
                    $this->create_campaign_targets($campaign_id, $data['targets']);
                }

                // Create gifts if provided
                if (!empty($data['gifts'])) {
                    $this->create_campaign_gifts($campaign_id, $data['gifts']);
                }

                // Create rules if provided
                if (!empty($data['rules'])) {
                    $this->create_campaign_rules($campaign_id, $data['rules']);
                }

                $wpdb->query('COMMIT');
                do_action('fs_campaign_created', $campaign_id, $data);

                return $campaign_id;
            }

            $wpdb->query('ROLLBACK');
            return false;

        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            return false;
        }
    }
    
    /**
     * Update campaign with products
     */
    public function update_campaign($campaign_id, $data, $products = []) {
        global $wpdb;

        $table = FS_Database::get_table_name('campaigns');

        $campaign_data = [
            'name' => sanitize_text_field($data['name']),
            'description' => sanitize_textarea_field($data['description']),
            'type' => sanitize_text_field($data['type']),
            'priority' => intval($data['priority']),
            'status' => intval($data['status']),
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'global_settings' => isset($data['global_settings']) ? json_encode($data['global_settings']) : '{}',
            'updated_at' => current_time('mysql')
        ];

        $wpdb->query('START TRANSACTION');

        try {
            $result = $wpdb->update($table, $campaign_data, ['id' => $campaign_id]);

            if ($result !== false) {
                // Update products if provided
                if (!empty($products)) {
                    $this->save_campaign_products($campaign_id, $products);
                }

                $wpdb->query('COMMIT');
                do_action('fs_campaign_updated', $campaign_id, $data);
                return true;
            }

            $wpdb->query('ROLLBACK');
            return false;

        } catch (Exception $e) {
            $wpdb->query('ROLLBACK');
            return false;
        }
    }

    /**
     * Save campaign products
     */
    public function save_campaign_products($campaign_id, $products) {
        global $wpdb;

        $products_table = FS_Database::get_table_name('campaign_products');

        // Delete existing products for this campaign
        $wpdb->delete($products_table, ['promotion_id' => $campaign_id]);

        // Insert new products
        foreach ($products as $product_id => $product_data) {
            $product_record = [
                'promotion_id' => $campaign_id,
                'product_id' => intval($product_id),
                'discount_type' => $product_data['discount_type'] === 'percentage' ? 1 : 2,
                'discount_value' => floatval($product_data['discount_value']),
                'max_discount_amount' => floatval($product_data['max_discount'] ?? 0),
                'sold' => intval($product_data['sold_quantity'] ?? 0),
                'qty_max' => intval($product_data['max_quantity'] ?? 0),
                'created_at' => current_time('mysql')
            ];

            // Parse time range if provided
            if (!empty($product_data['time_range'])) {
                $time_parts = explode(' - ', $product_data['time_range']);
                if (count($time_parts) === 2) {
                    $product_record['start_date'] = date('Y-m-d H:i:s', strtotime($time_parts[0]));
                    $product_record['end_date'] = date('Y-m-d H:i:s', strtotime($time_parts[1]));
                }
            }

            $wpdb->insert($products_table, $product_record);
        }

        return true;
    }
    
    /**
     * Remove specific product from campaign
     * TEMPORARILY DISABLED - Using DOM-only removal since hidden inputs are now inside table rows
     * Products will be removed when the campaign is saved (clear-and-insert strategy)
     */
    /*
    public function remove_campaign_product($campaign_id, $product_id) {
        global $wpdb;

        $products_table = FS_Database::get_table_name('campaign_products');

        // Debug logging
        error_log("FS_Campaign_Manager: Removing product - Campaign ID: $campaign_id, Product ID: $product_id");
        error_log("FS_Campaign_Manager: Table name: $products_table");

        // Check if records exist before deletion
        $existing_records = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $products_table WHERE promotion_id = %d AND product_id = %d",
            $campaign_id, $product_id
        ));

        error_log("FS_Campaign_Manager: Found " . count($existing_records) . " existing records");
        if (!empty($existing_records)) {
            error_log("FS_Campaign_Manager: Existing records: " . print_r($existing_records, true));
        }

        // Delete all records for this product in this campaign
        // This handles products with custom date ranges as well
        $result = $wpdb->delete($products_table, [
            'promotion_id' => $campaign_id,
            'product_id' => $product_id
        ]);

        error_log("FS_Campaign_Manager: Delete result: " . ($result !== false ? $result : 'false'));
        if ($result === false) {
            error_log("FS_Campaign_Manager: Delete error: " . $wpdb->last_error);
        }

        if ($result !== false) {
            // Clear cache
            if (class_exists('FS_Public')) {
                FS_Public::clear_product_promotions_cache($product_id);
                FS_Public::clear_campaigns_cache();
            }

            do_action('fs_campaign_product_removed', $campaign_id, $product_id);
            return true;
        }

        return false;
    }
    */
    
    /**
     * Delete campaign
     */
    public function delete_campaign($campaign_id) {
        global $wpdb;
        
        $table = FS_Database::get_table_name('campaigns');
        
        // Delete campaign (cascade will handle related records)
        $result = $wpdb->delete($table, ['id' => $campaign_id]);
        
        if ($result) {
            do_action('fs_campaign_deleted', $campaign_id);
            return true;
        }
        
        return false;
    }
    
    /**
     * Create campaign targets
     */
    public function create_campaign_targets($campaign_id, $targets) {
        global $wpdb;
        
        $table = FS_Database::get_table_name('campaign_targets');
        
        foreach ($targets as $target) {
            $target_data = [
                'campaign_id' => $campaign_id,
                'rule_id' => isset($target['rule_id']) ? $target['rule_id'] : null,
                'target_type' => sanitize_text_field($target['target_type']),
                'target_id' => intval($target['target_id']),
                'target_name' => sanitize_text_field($target['target_name']),
                'target_config' => json_encode($target['target_config']),
                'pricing_rules' => json_encode($target['pricing_rules']),
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ];
            
            $wpdb->insert($table, $target_data);
        }
    }
    
    /**
     * Create campaign gifts
     */
    public function create_campaign_gifts($campaign_id, $gifts) {
        global $wpdb;
        
        $table = FS_Database::get_table_name('campaign_gifts');
        
        foreach ($gifts as $gift) {
            $gift_data = [
                'campaign_id' => $campaign_id,
                'rule_id' => isset($gift['rule_id']) ? $gift['rule_id'] : null,
                'target_id' => isset($gift['target_id']) ? $gift['target_id'] : null,
                'gift_type' => sanitize_text_field($gift['gift_type']),
                'gift_id' => isset($gift['gift_id']) ? intval($gift['gift_id']) : null,
                'gift_name' => sanitize_text_field($gift['gift_name']),
                'gift_config' => json_encode($gift['gift_config']),
                'allow_selection' => intval($gift['allow_selection']),
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ];
            
            $wpdb->insert($table, $gift_data);
        }
    }
    
    /**
     * Create campaign rules
     */
    public function create_campaign_rules($campaign_id, $rules) {
        global $wpdb;
        
        $table = FS_Database::get_table_name('campaign_rules');
        
        foreach ($rules as $rule) {
            $rule_data = [
                'campaign_id' => $campaign_id,
                'rule_type' => sanitize_text_field($rule['rule_type']),
                'rule_name' => sanitize_text_field($rule['rule_name']),
                'conditions' => json_encode($rule['conditions']),
                'actions' => json_encode($rule['actions']),
                'priority' => intval($rule['priority']),
                'status' => intval($rule['status']),
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ];
            
            $wpdb->insert($table, $rule_data);
        }
    }
}
