/**
 * Admin styles for Quantity Promotion
 */

/* Campaign Form */
.fsqp-campaign-form {
    max-width: 100%;
}

.fsqp-campaign-form .fs-section {
    margin-bottom: 30px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
}

.fsqp-campaign-form .fs-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 18px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/* Rules Container */
.fsqp-rules-container {
    margin-top: 15px;
}

.fsqp-rules-header {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    align-items: center;
}

.fsqp-rules-table-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.fsqp-rules-table {
    margin: 0;
    border: none;
}

.fsqp-rules-table th,
.fsqp-rules-table td {
    padding: 12px 8px;
    text-align: left;
    vertical-align: middle;
}

.fsqp-rules-table th {
    background: #f9f9f9;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
}

.fsqp-rules-table input,
.fsqp-rules-table select {
    width: 100%;
    min-width: 80px;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
}

.fsqp-rules-table input[type="number"] {
    min-width: 60px;
}

.fsqp-product-select {
    min-width: 200px;
}

.fsqp-remove-rule {
    color: #a00;
    border-color: #a00;
}

.fsqp-remove-rule:hover {
    background: #a00;
    color: #fff;
}

.fsqp-no-rules {
    padding: 40px 20px;
    text-align: center;
    color: #666;
    background: #f9f9f9;
    border-top: 1px solid #ddd;
}

/* Preview Section */
.fsqp-preview-container {
    margin-top: 15px;
}

.fsqp-preview-content {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    margin-top: 10px;
}

.fsqp-preview-discount-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.fsqp-preview-discount-table th,
.fsqp-preview-discount-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.fsqp-preview-discount-table th {
    background: #2271b1;
    color: #fff;
    font-weight: 600;
}

.fsqp-preview-empty {
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Modal Styles */
.fsqp-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fsqp-modal-content {
    background: #fff;
    border-radius: 4px;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.fsqp-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ddd;
    background: #f9f9f9;
}

.fsqp-modal-header h3 {
    margin: 0;
    font-size: 18px;
}

.fsqp-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fsqp-modal-close:hover {
    color: #000;
}

.fsqp-modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Search Form */
.fsqp-search-form {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.fsqp-search-form input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.fsqp-search-results {
    min-height: 200px;
}

.fsqp-search-loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.fsqp-search-results-list {
    display: grid;
    gap: 10px;
}

.fsqp-product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    transition: all 0.2s ease;
}

.fsqp-product-item:hover {
    border-color: #2271b1;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.fsqp-product-info h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    font-weight: 600;
}

.fsqp-product-info p {
    margin: 0;
    font-size: 12px;
    color: #666;
}

.fsqp-product-price {
    font-weight: 600;
    color: #2271b1;
}

.fsqp-add-product-btn {
    margin-left: 15px;
}

/* Field Groups */
.fs-field-group {
    display: grid;
    gap: 15px;
}

.fs-field label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.fs-field input[type="checkbox"] {
    width: auto !important;
    margin: 0;
}

.fs-field .description {
    margin: 5px 0 0 0;
    font-size: 13px;
    color: #666;
    font-style: italic;
}

/* Responsive */
@media (max-width: 768px) {
    .fsqp-rules-table-container {
        overflow-x: auto;
    }
    
    .fsqp-rules-table {
        min-width: 800px;
    }
    
    .fsqp-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .fsqp-search-form {
        flex-direction: column;
    }
    
    .fsqp-product-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .fsqp-add-product-btn {
        margin-left: 0;
        align-self: flex-end;
    }
}

/* Loading States */
.fsqp-loading {
    opacity: 0.6;
    pointer-events: none;
}

.fsqp-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: fsqp-spin 1s linear infinite;
}

@keyframes fsqp-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.fsqp-message {
    padding: 10px 15px;
    border-radius: 4px;
    margin: 10px 0;
}

.fsqp-message.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.fsqp-message.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Validation Styles */
.fsqp-rule-row.has-error {
    background-color: #fff2f2;
}

.fsqp-rule-row.has-error input,
.fsqp-rule-row.has-error select {
    border-color: #dc3545;
}

.fsqp-validation-error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

/* Highlight active rule */
.fsqp-rule-row:hover {
    background-color: #f8f9fa;
}

/* Button States */
.fsqp-add-rule:disabled,
.fsqp-search-products:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Product Type Badges for Flash Sale */
.fs-product-type {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    margin-left: 5px;
}

.fs-product-type.fs-variation {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.fs-product-type.fs-variable {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #e1bee7;
}

/* Enhanced search results for Flash Sale */
.fs-search-result-item[data-product-type="variation"] {
    border-left: 3px solid #2196f3;
}

.fs-search-result-item[data-product-type="variable"] {
    border-left: 3px solid #9c27b0;
}

/* Clear button styling for Flash Sale */
.fs-btn-warning {
    background-color: #ff9800;
    color: white;
    border: 1px solid #f57c00;
}

.fs-btn-warning:hover {
    background-color: #f57c00;
    border-color: #ef6c00;
}

/* Enhanced Loading Styles */
.fs-loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: fs-spin 1s linear infinite;
    vertical-align: middle;
}

.fs-btn .fs-loading-spinner {
    margin-right: 5px;
}

.fs-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* Search Loading States */
.fs-search-input-group.loading .fs-search-loading {
    display: block;
}

.fs-search-loading {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    display: none;
    z-index: 10;
}

.fs-search-loading .fs-loading-spinner {
    width: 18px;
    height: 18px;
    border-width: 2px;
}

/* Product Search Results Enhanced */
.fs-search-results {
    position: relative;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    margin-top: 8px;
}

.fs-search-results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
    border-radius: 6px 6px 0 0;
}

.fs-search-results-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.fs-search-result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s ease;
    cursor: pointer;
}

.fs-search-result-item:hover {
    background-color: #f8f9fa;
    border-left: 3px solid #0073aa;
}

.fs-search-result-item:last-child {
    border-bottom: none;
    border-radius: 0 0 6px 6px;
}

.fs-search-result-item .fs-product-info {
    flex: 1;
}

.fs-search-result-item .fs-product-info strong {
    display: block;
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
}

.fs-search-result-item .fs-product-meta {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.fs-search-result-item .fs-product-meta span {
    margin-right: 12px;
}

.fs-search-result-item .fs-add-product {
    margin-left: 12px;
    padding: 6px 12px;
    font-size: 12px;
    white-space: nowrap;
}

/* Remove Product Button Enhanced */
.fs-remove-product {
    position: relative;
    overflow: hidden;
}

.fs-remove-product:disabled .fs-loading-spinner {
    animation: fs-spin 1s linear infinite;
}

/* Animation for smooth transitions */
@keyframes fs-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive improvements */
@media (max-width: 768px) {
    .fs-search-results {
        max-height: 300px;
    }

    .fs-search-result-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .fs-search-result-item .fs-add-product {
        margin-left: 0;
        align-self: flex-end;
    }
}

/* Shortcode Section Styles */
.fs-shortcode-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.fs-shortcode-item {
    margin-bottom: 25px;
    padding: 20px;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.fs-shortcode-item:last-child {
    margin-bottom: 0;
}

.fs-shortcode-wrapper {
    display: flex;
    gap: 10px;
    align-items: center;
    margin: 10px 0;
}

.fs-shortcode-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    background: #f8f9fa;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: #2c3e50;
    transition: all 0.3s ease;
}

.fs-shortcode-input:focus {
    outline: none;
    border-color: #0073aa;
    background: #fff;
    box-shadow: 0 0 0 3px rgba(0, 115, 170, 0.1);
}

.fs-btn-copy {
    padding: 12px 20px;
    background: #0073aa;
    color: #fff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.fs-btn-copy:hover {
    background: #005a87;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.fs-btn-copy.fs-btn-success {
    background: #28a745;
    animation: pulse 0.3s ease;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.fs-help-text {
    display: block;
    margin-top: 8px;
    color: #6c757d;
    font-size: 13px;
    font-style: italic;
}

/* Shortcode Builder */
.fs-shortcode-builder {
    background: #fff;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.fs-shortcode-builder h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
}

.fs-builder-controls {
    display: flex;
    gap: 15px;
    align-items: end;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.fs-form-group-inline {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.fs-form-group-inline .fs-label {
    font-size: 13px;
    font-weight: 500;
    color: #495057;
}

.fs-input-small,
.fs-select-small {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    min-width: 80px;
}

.fs-input-small:focus,
.fs-select-small:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.1);
}

/* Shortcode Parameters */
.fs-shortcode-params {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 20px;
    margin-top: 20px;
}

.fs-shortcode-params h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 10px;
}

.fs-params-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.fs-param-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #0073aa;
}

.fs-param-item strong {
    color: #2c3e50;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.fs-param-item span {
    color: #6c757d;
    font-size: 13px;
}

/* Card Description */
.fs-card-description {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
    font-style: italic;
}

/* Shortcode Responsive */
@media (max-width: 768px) {
    .fs-shortcode-wrapper {
        flex-direction: column;
        align-items: stretch;
    }

    .fs-builder-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .fs-params-grid {
        grid-template-columns: 1fr;
    }

    .fs-shortcode-input {
        font-size: 12px;
        padding: 10px 12px;
    }

    .fs-btn-copy {
        padding: 10px 16px;
        justify-content: center;
    }
}

/* Compact Shortcode Styles for Campaign List */
.fs-campaign-shortcode {
    margin: 15px 0;
    padding: 12px;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.fs-shortcode-label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 6px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.fs-shortcode-wrapper-compact {
    display: flex;
    gap: 6px;
    align-items: center;
}

.fs-shortcode-input-compact {
    flex: 1;
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 3px;
    background: #fff;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #495057;
    transition: all 0.2s ease;
}

.fs-shortcode-input-compact:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 2px rgba(0, 115, 170, 0.1);
}

.fs-btn-copy-compact {
    padding: 6px 8px;
    background: #0073aa;
    color: #fff;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

.fs-btn-copy-compact:hover {
    background: #005a87;
    transform: translateY(-1px);
}

.fs-btn-copy-compact.fs-btn-success {
    background: #28a745;
    animation: compactPulse 0.3s ease;
}

@keyframes compactPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.fs-btn-copy-compact .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Toast Notifications */
.fs-toast {
    position: fixed;
    top: 32px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: #fff;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.fs-toast.fs-toast-show {
    transform: translateX(0);
}

.fs-toast-success {
    background: #28a745;
}

.fs-toast-warning {
    background: #ffc107;
    color: #212529;
}

.fs-toast-error {
    background: #dc3545;
}

.fs-toast-info {
    background: #17a2b8;
}

/* Campaign Card Responsive */
@media (max-width: 768px) {
    .fs-shortcode-wrapper-compact {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .fs-btn-copy-compact {
        height: auto;
        padding: 8px 12px;
        min-width: auto;
    }

    .fs-shortcode-input-compact {
        font-size: 11px;
    }
}
