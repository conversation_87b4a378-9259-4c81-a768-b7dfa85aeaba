<?php
/**
 * Edit campaign page template
 *
 * @package FlashSale_Core
 * @since 2.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

// Get campaign data
$campaign = isset($campaign) ? $campaign : null;
if (!$campaign) {
    echo '<div class="notice notice-error"><p>' . __('Campaign not found.', 'flashsale-core') . '</p></div>';
    return;
}
?>

<div class="wrap fs-admin-wrap">
    <div class="fs-top-page">
        <h1 class="fs-title">
            <span class="fs-icon">✏️</span>
            <?php printf(__('Edit Campaign: %s', 'flashsale-core'), esc_html($campaign->name)); ?>
        </h1>
        <div class="fs-top-page-actions">
            <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns'); ?>" class="fs-btn fs-btn-secondary">
                <span class="dashicons dashicons-arrow-left-alt"></span>
                <?php _e('Back to Campaigns', 'flashsale-core'); ?>
            </a>
        </div>
    </div>

    <div class="fs-dashboard">
        <form id="fs-campaign-form" method="post">
            <?php wp_nonce_field('fs_campaign_nonce', '_wpnonce'); ?>
            <input type="hidden" name="campaign_id" value="<?php echo $campaign->id; ?>">
            
            <div class="fs-form-grid">
                <!-- Basic Information -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Basic Information', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-form-group">
                            <label for="campaign_name" class="fs-label">
                                <?php _e('Campaign Name', 'flashsale-core'); ?>
                                <span class="fs-required">*</span>
                            </label>
                            <input type="text" 
                                   id="campaign_name" 
                                   name="campaign_name" 
                                   class="fs-input" 
                                   value="<?php echo esc_attr($campaign->name); ?>"
                                   required>
                        </div>
                        
                        <div class="fs-form-group">
                            <label for="campaign_description" class="fs-label">
                                <?php _e('Description', 'flashsale-core'); ?>
                            </label>
                            <textarea id="campaign_description" 
                                      name="campaign_description" 
                                      class="fs-textarea" 
                                      rows="3"><?php echo esc_textarea($campaign->description); ?></textarea>
                        </div>
                        
                        <div class="fs-form-row">
                            <div class="fs-form-group">
                                <label for="campaign_type" class="fs-label">
                                    <?php _e('Campaign Type', 'flashsale-core'); ?>
                                    <span class="fs-required">*</span>
                                </label>
                                <select id="campaign_type" name="campaign_type" class="fs-select" required>
                                    <option value=""><?php _e('Select campaign type', 'flashsale-core'); ?></option>
                                    <option value="flash-sale" <?php selected($campaign->type, 'flash-sale'); ?>>
                                        <?php _e('Flash Sale', 'flashsale-core'); ?>
                                    </option>
                                </select>
                            </div>
                            
                            <div class="fs-form-group">
                                <label for="campaign_priority" class="fs-label">
                                    <?php _e('Priority', 'flashsale-core'); ?>
                                </label>
                                <select id="campaign_priority" name="campaign_priority" class="fs-select">
                                    <?php for ($i = 0; $i <= 10; $i++): ?>
                                        <option value="<?php echo $i; ?>" <?php selected($campaign->priority, $i); ?>>
                                            <?php echo $i; ?>
                                        </option>
                                    <?php endfor; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Schedule -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Schedule', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-form-row">
                            <div class="fs-form-group">
                                <label for="start_date" class="fs-label">
                                    <?php _e('Start Date & Time', 'flashsale-core'); ?>
                                </label>
                                <input type="datetime-local" 
                                       id="start_date" 
                                       name="start_date" 
                                       class="fs-input"
                                       value="<?php echo $campaign->start_date ? date('Y-m-d\TH:i', strtotime($campaign->start_date)) : ''; ?>">
                            </div>
                            
                            <div class="fs-form-group">
                                <label for="end_date" class="fs-label">
                                    <?php _e('End Date & Time', 'flashsale-core'); ?>
                                </label>
                                <input type="datetime-local" 
                                       id="end_date" 
                                       name="end_date" 
                                       class="fs-input"
                                       value="<?php echo $campaign->end_date ? date('Y-m-d\TH:i', strtotime($campaign->end_date)) : ''; ?>"
                                       <?php echo !$campaign->end_date ? 'disabled' : ''; ?>>
                            </div>
                        </div>
                        
                        <div class="fs-form-group">
                            <label class="fs-checkbox-label">
                                <input type="checkbox" 
                                       id="no_end_date" 
                                       name="no_end_date" 
                                       class="fs-checkbox"
                                       <?php checked(!$campaign->end_date); ?>>
                                <span class="fs-checkmark"></span>
                                <?php _e('No end date (run indefinitely)', 'flashsale-core'); ?>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Campaign Statistics -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Statistics', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-stats-mini">
                            <div class="fs-stat-mini">
                                <span class="fs-stat-mini-label"><?php _e('Created', 'flashsale-core'); ?></span>
                                <span class="fs-stat-mini-value">
                                    <?php echo date('M j, Y H:i', strtotime($campaign->created_at)); ?>
                                </span>
                            </div>
                            
                            <div class="fs-stat-mini">
                                <span class="fs-stat-mini-label"><?php _e('Last Updated', 'flashsale-core'); ?></span>
                                <span class="fs-stat-mini-value">
                                    <?php echo $campaign->updated_at ? date('M j, Y H:i', strtotime($campaign->updated_at)) : '-'; ?>
                                </span>
                            </div>
                            
                            <div class="fs-stat-mini">
                                <span class="fs-stat-mini-label"><?php _e('Total Sales', 'flashsale-core'); ?></span>
                                <span class="fs-stat-mini-value">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status -->
                <div class="fs-card">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Status', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-form-group">
                            <label class="fs-checkbox-label">
                                <input type="checkbox" 
                                       id="campaign_status" 
                                       name="campaign_status" 
                                       class="fs-checkbox" 
                                       value="1" 
                                       <?php checked($campaign->status); ?>>
                                <span class="fs-checkmark"></span>
                                <?php _e('Campaign is active', 'flashsale-core'); ?>
                            </label>
                        </div>
                        
                        <div class="fs-current-status">
                            <span class="fs-status-label"><?php _e('Current Status:', 'flashsale-core'); ?></span>
                            <?php if ($campaign->status): ?>
                                <span class="fs-status fs-status-active"><?php _e('Active', 'flashsale-core'); ?></span>
                            <?php else: ?>
                                <span class="fs-status fs-status-inactive"><?php _e('Inactive', 'flashsale-core'); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <?php if ($campaign->type === 'flash-sale'): ?>
                <!-- Shortcode Display -->
                <div class="fs-card fs-full-width" id="campaign-shortcode">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Campaign Shortcode', 'flashsale-core'); ?></h2>
                        <p class="fs-card-description"><?php _e('Use this shortcode to display the flash sale campaign on any page or post.', 'flashsale-core'); ?></p>
                    </div>
                    <div class="fs-card-content">
                        <div class="fs-shortcode-section">
                            <!-- Basic Shortcode -->
                            <div class="fs-shortcode-item">
                                <label class="fs-label"><?php _e('Basic Shortcode', 'flashsale-core'); ?></label>
                                <div class="fs-shortcode-wrapper">
                                    <input type="text"
                                           class="fs-shortcode-input"
                                           value="[flashsale_campaign id=&quot;<?php echo $campaign->id; ?>&quot;]"
                                           readonly>
                                    <button type="button" class="fs-btn fs-btn-primary fs-btn-copy" data-copy="[flashsale_campaign id=&quot;<?php echo $campaign->id; ?>&quot;]">
                                        <span class="dashicons dashicons-admin-page"></span>
                                        <?php _e('Copy', 'flashsale-core'); ?>
                                    </button>
                                </div>
                                <small class="fs-help-text"><?php _e('Displays the campaign with default settings (10 products, 4 columns)', 'flashsale-core'); ?></small>
                            </div>

                            <!-- Advanced Shortcode -->
                            <div class="fs-shortcode-item">
                                <label class="fs-label"><?php _e('Advanced Shortcode', 'flashsale-core'); ?></label>
                                <div class="fs-shortcode-wrapper">
                                    <input type="text"
                                           class="fs-shortcode-input"
                                           value="[flashsale_campaign id=&quot;<?php echo $campaign->id; ?>&quot; limit=&quot;10&quot; columns=&quot;4&quot;]"
                                           readonly>
                                    <button type="button" class="fs-btn fs-btn-primary fs-btn-copy" data-copy="[flashsale_campaign id=&quot;<?php echo $campaign->id; ?>&quot; limit=&quot;10&quot; columns=&quot;4&quot;]">
                                        <span class="dashicons dashicons-admin-page"></span>
                                        <?php _e('Copy', 'flashsale-core'); ?>
                                    </button>
                                </div>
                                <small class="fs-help-text"><?php _e('Customize the number of products (limit) and columns displayed', 'flashsale-core'); ?></small>
                            </div>

                            <!-- Shortcode Builder -->
                            <div class="fs-shortcode-builder">
                                <h4><?php _e('Shortcode Builder', 'flashsale-core'); ?></h4>
                                <div class="fs-builder-controls">
                                    <div class="fs-form-group fs-form-group-inline">
                                        <label class="fs-label"><?php _e('Limit:', 'flashsale-core'); ?></label>
                                        <input type="number" id="shortcode-limit" class="fs-input fs-input-small" value="10" min="1" max="50">
                                    </div>
                                    <div class="fs-form-group fs-form-group-inline">
                                        <label class="fs-label"><?php _e('Columns:', 'flashsale-core'); ?></label>
                                        <select id="shortcode-columns" class="fs-select fs-select-small">
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4" selected>4</option>
                                            <option value="5">5</option>
                                            <option value="6">6</option>
                                        </select>
                                    </div>
                                    <button type="button" class="fs-btn fs-btn-secondary" id="generate-shortcode">
                                        <?php _e('Generate', 'flashsale-core'); ?>
                                    </button>
                                </div>
                                <div class="fs-shortcode-wrapper">
                                    <input type="text"
                                           id="generated-shortcode"
                                           class="fs-shortcode-input"
                                           value="[flashsale_campaign id=&quot;<?php echo $campaign->id; ?>&quot; limit=&quot;10&quot; columns=&quot;4&quot;]"
                                           readonly>
                                    <button type="button" class="fs-btn fs-btn-primary fs-btn-copy" id="copy-generated">
                                        <span class="dashicons dashicons-admin-page"></span>
                                        <?php _e('Copy', 'flashsale-core'); ?>
                                    </button>
                                </div>
                            </div>

                            <!-- Shortcode Parameters -->
                            <div class="fs-shortcode-params">
                                <h4><?php _e('Available Parameters', 'flashsale-core'); ?></h4>
                                <div class="fs-params-grid">
                                    <div class="fs-param-item">
                                        <strong>id</strong>
                                        <span><?php _e('Campaign ID (required)', 'flashsale-core'); ?></span>
                                    </div>
                                    <div class="fs-param-item">
                                        <strong>limit</strong>
                                        <span><?php _e('Number of products to display (default: 10)', 'flashsale-core'); ?></span>
                                    </div>
                                    <div class="fs-param-item">
                                        <strong>columns</strong>
                                        <span><?php _e('Number of columns (default: 4)', 'flashsale-core'); ?></span>
                                    </div>
                                    <div class="fs-param-item">
                                        <strong>type</strong>
                                        <span><?php _e('Campaign type (optional)', 'flashsale-core'); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Campaign Settings -->
                <div class="fs-card fs-full-width" id="campaign-settings">
                    <div class="fs-card-top-page">
                        <h2><?php _e('Campaign Settings', 'flashsale-core'); ?></h2>
                    </div>
                    <div class="fs-card-content">
                        <?php
                        // Load campaign-specific settings based on type
                        $global_settings = json_decode($campaign->global_settings, true);
                        ?>
                        
                        <div id="current-settings">
                            <h3><?php printf(__('%s Settings', 'flashsale-core'), ucfirst(str_replace('-', ' ', $campaign->type))); ?></h3>
                            
                            <?php if ($campaign->type === 'flash-sale'): ?>
                                <div class="fs-form-group">
                                    <label class="fs-label"><?php _e('Default Discount Type', 'flashsale-core'); ?></label>
                                    <select name="discount_type" class="fs-select">
                                        <option value="percentage" <?php selected($global_settings['discount_type'] ?? '', 'percentage'); ?>>
                                            <?php _e('Percentage', 'flashsale-core'); ?>
                                        </option>
                                        <option value="fixed" <?php selected($global_settings['discount_type'] ?? '', 'fixed'); ?>>
                                            <?php _e('Fixed Amount', 'flashsale-core'); ?>
                                        </option>
                                    </select>
                                </div>

                                <div class="fs-form-group">
                                    <label class="fs-label"><?php _e('Default Discount Value', 'flashsale-core'); ?></label>
                                    <input type="number"
                                           name="discount_value"
                                           class="fs-input"
                                           value="<?php echo esc_attr($global_settings['discount_value'] ?? ''); ?>"
                                           min="0"
                                           step="0.01">
                                </div>

                                <div class="fs-form-group">
                                    <label class="fs-label"><?php _e('Default Max Quantity per Customer', 'flashsale-core'); ?></label>
                                    <input type="number"
                                           name="max_quantity"
                                           class="fs-input"
                                           value="<?php echo esc_attr($global_settings['max_quantity'] ?? ''); ?>"
                                           min="0">
                                </div>
                            <?php endif; ?>
                            
                            <?php if (empty($global_settings)): ?>
                                <div class="fs-empty-state">
                                    <span class="dashicons dashicons-admin-settings"></span>
                                    <h3><?php _e('No settings configured', 'flashsale-core'); ?></h3>
                                    <p><?php _e('This campaign doesn\'t have any specific settings configured yet.', 'flashsale-core'); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="fs-form-actions">
                <button type="submit" class="fs-btn fs-btn-primary fs-btn-large">
                    <span class="dashicons dashicons-yes"></span>
                    <?php _e('Update Campaign', 'flashsale-core'); ?>
                </button>
                
                <button type="button" class="fs-btn fs-btn-danger fs-btn-large" id="delete-campaign">
                    <span class="dashicons dashicons-trash"></span>
                    <?php _e('Delete Campaign', 'flashsale-core'); ?>
                </button>
                
                <a href="<?php echo admin_url('admin.php?page=flashsale-campaigns'); ?>" class="fs-btn fs-btn-outline fs-btn-large">
                    <?php _e('Cancel', 'flashsale-core'); ?>
                </a>
            </div>
        </form>
    </div>
</div>



<script>
jQuery(document).ready(function($) {
    // No end date checkbox
    $('#no_end_date').on('change', function() {
        if ($(this).is(':checked')) {
            $('#end_date').prop('disabled', true).val('');
        } else {
            $('#end_date').prop('disabled', false);
        }
    });
    
    // Delete campaign
    $('#delete-campaign').on('click', function() {
        if (confirm('<?php _e('Are you sure you want to delete this campaign? This action cannot be undone.', 'flashsale-core'); ?>')) {
            // Create a form to submit delete request
            var form = $('<form>', {
                'method': 'POST',
                'action': window.location.href
            });
            
            form.append($('<input>', {
                'type': 'hidden',
                'name': 'action',
                'value': 'delete'
            }));
            
            form.append($('<input>', {
                'type': 'hidden',
                'name': 'campaign_id',
                'value': '<?php echo $campaign->id; ?>'
            }));
            
            form.append($('<input>', {
                'type': 'hidden',
                'name': '_wpnonce',
                'value': '<?php echo wp_create_nonce('fs_campaign_nonce'); ?>'
            }));
            
            $('body').append(form);
            form.submit();
        }
    });
    
    <?php if ($campaign->type === 'flash-sale'): ?>
    // Shortcode copy functionality
    $('.fs-btn-copy').on('click', function() {
        var textToCopy = $(this).data('copy') || $(this).siblings('.fs-shortcode-input').val();

        // Create temporary textarea for copying
        var tempTextarea = $('<textarea>');
        tempTextarea.val(textToCopy);
        $('body').append(tempTextarea);
        tempTextarea.select();

        try {
            document.execCommand('copy');

            // Show success feedback
            var originalText = $(this).html();
            $(this).html('<span class="dashicons dashicons-yes"></span> <?php _e('Copied!', 'flashsale-core'); ?>');
            $(this).addClass('fs-btn-success');

            setTimeout(() => {
                $(this).html(originalText);
                $(this).removeClass('fs-btn-success');
            }, 2000);

        } catch (err) {
            // Fallback: select the input text
            $(this).siblings('.fs-shortcode-input').select();
            alert('<?php _e('Please copy the selected text manually (Ctrl+C)', 'flashsale-core'); ?>');
        }

        tempTextarea.remove();
    });

    // Shortcode builder
    $('#generate-shortcode').on('click', function() {
        var campaignId = '<?php echo $campaign->id; ?>';
        var limit = $('#shortcode-limit').val() || 10;
        var columns = $('#shortcode-columns').val() || 4;

        var shortcode = '[flashsale_campaign id="' + campaignId + '" limit="' + limit + '" columns="' + columns + '"]';
        $('#generated-shortcode').val(shortcode);

        // Update copy button data
        $('#copy-generated').data('copy', shortcode);
    });

    // Auto-generate when inputs change
    $('#shortcode-limit, #shortcode-columns').on('change input', function() {
        $('#generate-shortcode').click();
    });

    // Copy generated shortcode
    $('#copy-generated').on('click', function() {
        var shortcode = $('#generated-shortcode').val();

        // Create temporary textarea for copying
        var tempTextarea = $('<textarea>');
        tempTextarea.val(shortcode);
        $('body').append(tempTextarea);
        tempTextarea.select();

        try {
            document.execCommand('copy');

            // Show success feedback
            var originalText = $(this).html();
            $(this).html('<span class="dashicons dashicons-yes"></span> <?php _e('Copied!', 'flashsale-core'); ?>');
            $(this).addClass('fs-btn-success');

            setTimeout(() => {
                $(this).html(originalText);
                $(this).removeClass('fs-btn-success');
            }, 2000);

        } catch (err) {
            // Fallback: select the input text
            $('#generated-shortcode').select();
            alert('<?php _e('Please copy the selected text manually (Ctrl+C)', 'flashsale-core'); ?>');
        }

        tempTextarea.remove();
    });
    <?php endif; ?>

    // Form validation
    $('#fs-campaign-form').on('submit', function(e) {
        var isValid = true;
        var errors = [];

        // Check required fields
        if (!$('#campaign_name').val().trim()) {
            errors.push('<?php _e('Campaign name is required', 'flashsale-core'); ?>');
            isValid = false;
        }

        if (!$('#campaign_type').val()) {
            errors.push('<?php _e('Campaign type is required', 'flashsale-core'); ?>');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            alert(errors.join('\n'));
        }
    });
});
</script>
